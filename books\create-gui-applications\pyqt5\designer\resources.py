# -*- coding: utf-8 -*-

# Resource object code
#
# Created by: The Resource Compiler for PyQt5 (Qt v5.12.1)
#
# WARNING! All changes made in this file will be lost!

from PyQt5 import QtCore

qt_resource_data = b"\
\x00\x00\x06\x35\
\x89\
\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xff\x61\
\x00\x00\x00\x19\x74\x45\x58\x74\x53\x6f\x66\x74\x77\x61\x72\x65\
\x00\x41\x64\x6f\x62\x65\x20\x49\x6d\x61\x67\x65\x52\x65\x61\x64\
\x79\x71\xc9\x65\x3c\x00\x00\x03\x69\x69\x54\x58\x74\x58\x4d\x4c\
\x3a\x63\x6f\x6d\x2e\x61\x64\x6f\x62\x65\x2e\x78\x6d\x70\x00\x00\
\x00\x00\x00\x3c\x3f\x78\x70\x61\x63\x6b\x65\x74\x20\x62\x65\x67\
\x69\x6e\x3d\x22\xef\xbb\xbf\x22\x20\x69\x64\x3d\x22\x57\x35\x4d\
\x30\x4d\x70\x43\x65\x68\x69\x48\x7a\x72\x65\x53\x7a\x4e\x54\x63\
\x7a\x6b\x63\x39\x64\x22\x3f\x3e\x20\x3c\x78\x3a\x78\x6d\x70\x6d\
\x65\x74\x61\x20\x78\x6d\x6c\x6e\x73\x3a\x78\x3d\x22\x61\x64\x6f\
\x62\x65\x3a\x6e\x73\x3a\x6d\x65\x74\x61\x2f\x22\x20\x78\x3a\x78\
\x6d\x70\x74\x6b\x3d\x22\x41\x64\x6f\x62\x65\x20\x58\x4d\x50\x20\
\x43\x6f\x72\x65\x20\x35\x2e\x30\x2d\x63\x30\x36\x30\x20\x36\x31\
\x2e\x31\x33\x34\x37\x37\x37\x2c\x20\x32\x30\x31\x30\x2f\x30\x32\
\x2f\x31\x32\x2d\x31\x37\x3a\x33\x32\x3a\x30\x30\x20\x20\x20\x20\
\x20\x20\x20\x20\x22\x3e\x20\x3c\x72\x64\x66\x3a\x52\x44\x46\x20\
\x78\x6d\x6c\x6e\x73\x3a\x72\x64\x66\x3d\x22\x68\x74\x74\x70\x3a\
\x2f\x2f\x77\x77\x77\x2e\x77\x33\x2e\x6f\x72\x67\x2f\x31\x39\x39\
\x39\x2f\x30\x32\x2f\x32\x32\x2d\x72\x64\x66\x2d\x73\x79\x6e\x74\
\x61\x78\x2d\x6e\x73\x23\x22\x3e\x20\x3c\x72\x64\x66\x3a\x44\x65\
\x73\x63\x72\x69\x70\x74\x69\x6f\x6e\x20\x72\x64\x66\x3a\x61\x62\
\x6f\x75\x74\x3d\x22\x22\x20\x78\x6d\x6c\x6e\x73\x3a\x78\x6d\x70\
\x52\x69\x67\x68\x74\x73\x3d\x22\x68\x74\x74\x70\x3a\x2f\x2f\x6e\
\x73\x2e\x61\x64\x6f\x62\x65\x2e\x63\x6f\x6d\x2f\x78\x61\x70\x2f\
\x31\x2e\x30\x2f\x72\x69\x67\x68\x74\x73\x2f\x22\x20\x78\x6d\x6c\
\x6e\x73\x3a\x78\x6d\x70\x4d\x4d\x3d\x22\x68\x74\x74\x70\x3a\x2f\
\x2f\x6e\x73\x2e\x61\x64\x6f\x62\x65\x2e\x63\x6f\x6d\x2f\x78\x61\
\x70\x2f\x31\x2e\x30\x2f\x6d\x6d\x2f\x22\x20\x78\x6d\x6c\x6e\x73\
\x3a\x73\x74\x52\x65\x66\x3d\x22\x68\x74\x74\x70\x3a\x2f\x2f\x6e\
\x73\x2e\x61\x64\x6f\x62\x65\x2e\x63\x6f\x6d\x2f\x78\x61\x70\x2f\
\x31\x2e\x30\x2f\x73\x54\x79\x70\x65\x2f\x52\x65\x73\x6f\x75\x72\
\x63\x65\x52\x65\x66\x23\x22\x20\x78\x6d\x6c\x6e\x73\x3a\x78\x6d\
\x70\x3d\x22\x68\x74\x74\x70\x3a\x2f\x2f\x6e\x73\x2e\x61\x64\x6f\
\x62\x65\x2e\x63\x6f\x6d\x2f\x78\x61\x70\x2f\x31\x2e\x30\x2f\x22\
\x20\x78\x6d\x70\x52\x69\x67\x68\x74\x73\x3a\x4d\x61\x72\x6b\x65\
\x64\x3d\x22\x46\x61\x6c\x73\x65\x22\x20\x78\x6d\x70\x4d\x4d\x3a\
\x44\x6f\x63\x75\x6d\x65\x6e\x74\x49\x44\x3d\x22\x78\x6d\x70\x2e\
\x64\x69\x64\x3a\x31\x33\x31\x30\x38\x44\x32\x34\x43\x33\x31\x42\
\x31\x31\x45\x30\x42\x33\x36\x33\x46\x36\x35\x41\x44\x35\x36\x37\
\x38\x43\x31\x41\x22\x20\x78\x6d\x70\x4d\x4d\x3a\x49\x6e\x73\x74\
\x61\x6e\x63\x65\x49\x44\x3d\x22\x78\x6d\x70\x2e\x69\x69\x64\x3a\
\x31\x33\x31\x30\x38\x44\x32\x33\x43\x33\x31\x42\x31\x31\x45\x30\
\x42\x33\x36\x33\x46\x36\x35\x41\x44\x35\x36\x37\x38\x43\x31\x41\
\x22\x20\x78\x6d\x70\x3a\x43\x72\x65\x61\x74\x6f\x72\x54\x6f\x6f\
\x6c\x3d\x22\x41\x64\x6f\x62\x65\x20\x50\x68\x6f\x74\x6f\x73\x68\
\x6f\x70\x20\x43\x53\x33\x20\x57\x69\x6e\x64\x6f\x77\x73\x22\x3e\
\x20\x3c\x78\x6d\x70\x4d\x4d\x3a\x44\x65\x72\x69\x76\x65\x64\x46\
\x72\x6f\x6d\x20\x73\x74\x52\x65\x66\x3a\x69\x6e\x73\x74\x61\x6e\
\x63\x65\x49\x44\x3d\x22\x75\x75\x69\x64\x3a\x41\x43\x31\x46\x32\
\x45\x38\x33\x33\x32\x34\x41\x44\x46\x31\x31\x41\x41\x42\x38\x43\
\x35\x33\x39\x30\x44\x38\x35\x42\x35\x42\x33\x22\x20\x73\x74\x52\
\x65\x66\x3a\x64\x6f\x63\x75\x6d\x65\x6e\x74\x49\x44\x3d\x22\x75\
\x75\x69\x64\x3a\x43\x39\x44\x33\x34\x39\x36\x36\x34\x41\x33\x43\
\x44\x44\x31\x31\x42\x30\x38\x41\x42\x42\x42\x43\x46\x46\x31\x37\
\x32\x31\x35\x36\x22\x2f\x3e\x20\x3c\x2f\x72\x64\x66\x3a\x44\x65\
\x73\x63\x72\x69\x70\x74\x69\x6f\x6e\x3e\x20\x3c\x2f\x72\x64\x66\
\x3a\x52\x44\x46\x3e\x20\x3c\x2f\x78\x3a\x78\x6d\x70\x6d\x65\x74\
\x61\x3e\x20\x3c\x3f\x78\x70\x61\x63\x6b\x65\x74\x20\x65\x6e\x64\
\x3d\x22\x72\x22\x3f\x3e\x20\x11\x60\x13\x00\x00\x02\x62\x49\x44\
\x41\x54\x78\xda\xa4\x93\xcf\x6b\x13\x51\x10\xc7\x67\x7f\x34\xda\
\xb4\xc5\xc6\x18\x6b\x23\xd8\x24\x35\x82\xf4\xe0\xc1\xb0\xa2\x20\
\x01\x11\x23\x06\x6b\xc4\x83\x67\x2f\x1e\x7a\xf4\xe4\xa1\xd0\xf6\
\x20\x78\xf5\x22\xfe\xfc\x13\x04\x7f\x10\x50\x28\x78\x30\x08\x46\
\xd3\x34\xd2\x18\x6a\xa8\x1a\xa1\xa9\xc5\x26\x2f\xab\x71\x77\xb3\
\xc9\xee\x3a\xf3\xd8\xd8\x90\x83\x08\x0e\x7c\x78\xef\xcd\xfb\xce\
\xbc\x79\xf3\x76\x05\xc7\x71\xe0\x7f\x4c\x40\xeb\xf7\x49\xc8\x09\
\x24\x81\x4c\xb8\xbe\x0a\xf2\x02\x79\x8d\x58\x7f\x4b\xe0\x45\x66\
\xa2\xd1\x68\x2a\x99\x4c\x1e\x0a\x06\x83\xa3\xe4\xac\x56\xab\x8d\
\x74\x3a\xfd\xb1\x5c\x2e\x3f\xc6\xe5\x6d\x44\xfb\x73\x5a\x5f\x82\
\x99\x78\x3c\x7e\x75\x7e\x7e\xee\x48\x38\x1c\x19\x66\xac\x2e\x89\
\xa2\x20\xc5\x62\xb1\xa1\x44\xe2\xcc\xfe\x6a\x75\x63\xa2\x52\xa9\
\xd0\x9d\xdf\xf4\x96\xd0\x65\x2a\x10\x08\xe4\x97\x96\x72\x1d\x34\
\x87\x28\x14\x0a\x9c\xee\x9a\xf6\x48\x43\xda\x6e\x5c\xaf\xdd\x48\
\x24\x12\x75\x55\x55\x9d\x88\xcf\xe7\xe8\xba\xee\x34\x9b\x4d\x0e\
\xcd\xc9\x47\x7b\xa4\x21\x6d\x37\x48\x74\xc7\x8b\xc8\x95\xb1\xb1\
\xbd\x23\x9d\x4e\x1b\x56\xd6\xd7\xa1\xd5\x6a\xc1\xd0\x66\x8a\x43\
\x73\xf2\x59\x56\x07\x48\x43\x5a\x37\x06\x64\xe4\x12\x72\x57\x51\
\x14\xbf\x28\x8a\x60\x18\x06\x0f\x90\x24\x09\x3e\x2d\x2e\xf2\xec\
\xc3\x29\x03\x74\x5d\x03\xdb\xb6\x01\x35\x32\x6a\xc7\xb3\xd9\xec\
\x7d\x2a\x80\x12\x3c\x42\x36\xd1\xf1\x64\x72\xf2\xe0\x6e\x2c\x99\
\x44\x20\xcb\x32\xf8\x2f\x33\x9e\x80\x31\x86\xa7\x5b\x9c\x56\xab\
\x0d\xa8\xa5\x6b\xa4\x90\x8c\xec\x5e\x21\x83\xa8\x9a\x66\xf8\x6a\
\xb5\x9a\xe0\xf1\x78\xc0\xe3\x19\xc0\x53\x75\xde\x28\xba\x96\x69\
\x12\x26\xa0\x86\x5e\x41\x75\x63\x40\xee\x69\x62\x9e\x31\x35\x58\
\xab\xb1\x1d\x5e\xaf\x17\xab\xd8\xee\x32\x7d\xad\xb6\xed\x60\xb0\
\x86\xd5\xa8\x26\x69\xfb\x9b\x48\x76\xef\x43\x71\x99\xe9\x85\x5b\
\x8e\xf4\xeb\x33\x26\xf0\x60\xa0\xc4\x11\x84\x01\x20\x9f\x86\x7b\
\xa4\x21\x6d\x37\x88\x57\x50\x9c\xe3\xf3\xd0\xb3\x4a\xa7\x7d\xfa\
\xd8\x9a\x90\xcd\x2c\x38\xbb\x62\xd7\x05\xef\x81\xb3\x7c\x43\xfb\
\xfa\x1c\xd4\xdc\x4d\xe7\x94\x62\x0b\xd7\xce\x99\xed\x0b\x11\x08\
\x91\x7f\x6a\xc1\xcd\xa2\x84\x78\x92\x77\xed\x95\x93\x56\xe9\xc1\
\x78\x63\x76\x5a\x64\x23\x83\xf0\xde\x2d\x35\x4f\xf3\xd9\xf3\x02\
\x2b\xde\xd9\xd3\x30\x96\x8f\x5b\xa4\x55\x42\xdb\x15\x8c\x66\xbf\
\x80\xef\x6d\x05\x56\x7f\x3c\x7c\x05\x1b\xdf\x00\xcc\x3a\x3c\x8d\
\xed\x83\x5c\xe9\x3b\xd4\x48\x74\x38\x00\x7e\x93\x39\x47\x4b\x2f\
\xb7\xa6\xeb\xc5\x2d\x58\xfb\x09\xab\x18\x13\xa6\x07\xa2\x2e\xed\
\x44\x06\xdd\x51\xf8\xc7\xbf\x98\x5e\xc2\x40\xf4\xdf\x02\x0c\x00\
\x1b\x16\x36\xbc\xe4\xeb\x47\x7a\x00\x00\x00\x00\x49\x45\x4e\x44\
\xae\x42\x60\x82\
"

qt_resource_name = b"\
\x00\x05\
\x00\x6f\xa6\x53\
\x00\x69\
\x00\x63\x00\x6f\x00\x6e\x00\x73\
\x00\x0b\
\x0b\x2c\xde\x87\
\x00\x70\
\x00\x65\x00\x6e\x00\x67\x00\x75\x00\x69\x00\x6e\x00\x2e\x00\x70\x00\x6e\x00\x67\
"

qt_resource_struct_v1 = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x10\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
"

qt_resource_struct_v2 = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x10\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x72\x31\xd5\xef\xd1\
"

qt_version = [int(v) for v in QtCore.qVersion().split(".")]
if qt_version < [5, 8, 0]:
    rcc_version = 1
    qt_resource_struct = qt_resource_struct_v1
else:
    rcc_version = 2
    qt_resource_struct = qt_resource_struct_v2


def qInitResources():
    QtCore.qRegisterResourceData(
        rcc_version,
        qt_resource_struct,
        qt_resource_name,
        qt_resource_data,
    )


def qCleanupResources():
    QtCore.qUnregisterResourceData(
        rcc_version,
        qt_resource_struct,
        qt_resource_name,
        qt_resource_data,
    )


qInitResources()
